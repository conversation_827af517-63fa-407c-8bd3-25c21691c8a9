/* Onboarding Modules View Styles */

/* Reset and ensure no viewport scrolling */
html, body {
    height: 100%;
    margin: 0;
    padding: 0;
}

.onboarding-body {
    height: 100vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.onboarding-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    min-height: 0;
}

/* Container hierarchy */
.onboarding-container.modules-view {
    padding: 0 !important;
    flex: 1;
    display: flex !important;
    flex-direction: column;
    overflow: hidden;
    min-height: 0;
    background: var(--bg-primary);
}

#content {
    width: 100%;
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
    overflow: hidden;
}

.onboarding-modules-wrapper {
    width: 100%;
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
    overflow: hidden;
    background: var(--bg-primary);
}

/* Split layout - main container */
.modules-view .onboarding-split {
    display: flex;
    flex: 1;
    width: 100%;
    gap: 0;
    overflow: hidden;
    min-height: 0;
    background: var(--bg-primary);
}

/* Chat Panel - Fixed width left panel */
.modules-view .chat-panel {
    background: var(--bg-primary);
    border-right: 1px solid var(--border-light);
    display: flex;
    flex-direction: column;
    width: 380px;
    flex-shrink: 0;
    overflow: hidden;
    align-self: stretch;
}

/* Chat Header - Fixed at top of chat panel */
.modules-view .chat-header {
    padding: var(--space-3) var(--space-4);
    border-bottom: 1px solid var(--border-light);
    background: var(--bg-secondary);
    flex-shrink: 0;
}

.modules-view .chat-header h3 {
    font-size: 1.4rem;
    font-weight: 600;
    margin: 0;
    color: var(--text-primary);
}

.modules-view .chat-header p {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin: var(--space-1) 0 0;
}

/* Chat Messages - Scrollable middle section */
.modules-view .chat-messages {
    flex: 1;
    padding: var(--space-3);
    overflow-y: auto;
    overflow-x: hidden;
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
    min-height: 0;
}

/* Chat Form - Fixed at bottom of chat panel */
.chat-form {
    display: flex;
    gap: var(--space-2);
    padding: var(--space-3);
    border-top: 1px solid var(--border-light);
    background: var(--bg-primary);
    flex-shrink: 0;
}

.chat-form textarea {
    margin: 0;
}

.chat-input {
    flex: 1;
    padding: var(--space-2);
    border: 1px solid var(--border-light);
    border-radius: 6px;
    font-size: 1.3rem;
    resize: none;
    background: var(--bg-primary);
    color: var(--text-primary);
    transition: all 0.2s ease;
    min-height: 3.6rem;
    max-height: 8rem;
}

.chat-input:focus {
    outline: none;
    border-color: var(--vertoie-orange);
    box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1);
}

.btn-send {
    padding: var(--space-2);
    background: var(--vertoie-orange);
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 3.6rem;
    height: 3.6rem;
    flex-shrink: 0;
}

.btn-send:hover {
    background: var(--vertoie-orange-dark);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);
}

/* Messages */
.message {
    max-width: 85%;
}

.message.user {
    align-self: flex-end;
}

.message.assistant {
    align-self: flex-start;
}

.message span {
    display: block;
    padding: var(--space-2) var(--space-3);
    border-radius: 8px;
    font-size: 1.3rem;
    line-height: 1.5;
}

.message.user span {
    background: var(--vertoie-orange);
    color: white;
}

.message.assistant span {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

/* AI Suggestions */
.discovery-questions {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-light);
    border-radius: 8px;
    padding: var(--space-3);
    margin-bottom: var(--space-3);
}

.discovery-questions h4 {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 var(--space-2) 0;
    display: flex;
    align-items: center;
    gap: var(--space-1);
}

.discovery-questions h4::before {
    content: "💡";
    font-size: 1.6rem;
}

.question-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.question-list li {
    padding: var(--space-1) 0;
    font-size: 1.2rem;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    padding-left: var(--space-3);
}

.question-list li::before {
    content: "→";
    position: absolute;
    left: 0;
    color: var(--vertoie-orange);
    transition: all 0.2s ease;
}

.question-list li:hover {
    color: var(--vertoie-orange);
    transform: translateX(2px);
}

.question-list li:hover::before {
    transform: translateX(2px);
}

/* Typing indicator */
.typing-indicator {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: var(--space-3) var(--space-4);
    background: var(--bg-tertiary);
    border-radius: 12px;
    max-width: 80px;
}

.typing-indicator span {
    width: 8px;
    height: 8px;
    background: var(--text-muted);
    border-radius: 50%;
    animation: typing 1.5s infinite;
}

.typing-indicator span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typing {
    0%, 60%, 100% {
        opacity: 0.3;
        transform: scale(0.8);
    }
    30% {
        opacity: 1;
        transform: scale(1);
    }
}

/* Modules Panel - Flexible right panel */
.modules-view .modules-panel {
    background: var(--bg-secondary);
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: hidden;
    min-width: 0;
}

/* Pricing Bar - Fixed at top of modules panel */
.pricing-bar {
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-light);
    padding: var(--space-2) var(--space-4);
    flex-shrink: 0;
}

.pricing-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--space-4);
}

.pricing-box {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    font-size: 1.4rem;
    color: var(--text-primary);
    flex-shrink: 0;
}

.subtitle {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin: 0;
    line-height: 1.2;
}

.modules-view .modules-header {
    display: flex;
    flex-direction: column;
    gap: var(--space-1);
}

.modules-view .modules-header h2 {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
    line-height: 1.2;
}

/* Selected Modules Count */
.selected-count {
    background: var(--vertoie-orange);
    color: white;
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-full);
    font-size: 1.4rem;
    font-weight: 600;
    margin-left: var(--space-3);
}

/* Module Grid Container - Scrollable middle section */
.modules-view #modules-grid {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    padding: var(--space-4);
    min-height: 0;
}

/* Modules Grid */
.modules-view .modules-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: var(--space-3);
}

.modules-view .module-card {
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: 12px;
    padding: var(--space-4);
    transition: all 0.2s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.modules-view .module-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.modules-view .module-card.selected {
    border-color: var(--vertoie-orange);
    background: linear-gradient(135deg, var(--bg-primary) 0%, rgba(255, 107, 53, 0.05) 100%);
}

.modules-view .module-card.selected::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--vertoie-orange);
}

/* Module Header */
.module-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--space-3);
}

.module-header h4 {
    font-size: 1.6rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
    line-height: 1.4;
}

.module-checkbox {
    width: 2.4rem;
    height: 2.4rem;
    cursor: pointer;
    accent-color: var(--vertoie-orange);
    flex-shrink: 0;
}

/* Module Details */
.module-content {
    cursor: pointer;
}

.module-desc {
    font-size: 1.4rem;
    color: var(--text-secondary);
    line-height: 1.6;
    margin: var(--space-2) 0;
}

.module-benefit {
    font-size: 1.3rem;
    color: var(--color-success);
    font-style: italic;
    margin: var(--space-2) 0;
}

.module-expand {
    background: transparent;
    border: 1px solid var(--border-light);
    color: var(--text-secondary);
    padding: var(--space-2) var(--space-3);
    border-radius: 6px;
    font-size: 1.3rem;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-top: var(--space-3);
}

.module-expand:hover {
    border-color: var(--vertoie-orange);
    color: var(--vertoie-orange);
    background: rgba(255, 107, 53, 0.05);
}

/* Complete Section - Fixed at bottom of modules panel */
.complete-section {
    padding: var(--space-3) var(--space-4);
    display: flex;
    justify-content: center;
    background: var(--bg-primary);
    border-top: 1px solid var(--border-light);
    flex-shrink: 0;
}

.btn-complete {
    background: var(--vertoie-orange);
    color: white;
    padding: var(--space-3) var(--space-6);
    font-size: 1.6rem;
    font-weight: 600;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: var(--space-2);
    box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);
}

.btn-complete:hover {
    background: var(--vertoie-orange-dark);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4);
}

.btn-complete:disabled {
    background: var(--text-muted);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .modules-view .chat-panel {
        width: 340px;
    }
    
    .modules-view .modules-grid {
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    }
}

@media (max-width: 992px) {
    .modules-view .onboarding-split {
        flex-direction: column;
    }
    
    .modules-view .chat-panel {
        width: 100%;
        height: 300px;
        min-height: 300px;
        border-right: none;
        border-bottom: 1px solid var(--border-light);
    }
    
    .modules-view .modules-grid {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: var(--space-3);
    }
}

@media (max-width: 768px) {
    .modules-view .modules-panel {
        padding: var(--space-4);
    }
    
    .modules-view .modules-header {
        flex-direction: column;
        gap: var(--space-4);
    }
    
    .modules-view .modules-grid {
        grid-template-columns: 1fr;
    }
}

/* Animations */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading Spinner */
.spinner-large {
    width: 4rem;
    height: 4rem;
    border: 4px solid var(--border-light);
    border-top: 4px solid var(--vertoie-orange);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* HTMX Loading States */
.htmx-request .default-content {
    display: none;
}

.htmx-request .loading-content {
    display: block;
}

/* Success Animation */
.success-animation {
    display: inline-block;
    animation: successPulse 0.6s ease;
}

@keyframes successPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

/* Pricing Summary Styles */
.pricing-summary {
    display: flex;
    align-items: center;
    gap: var(--space-4);
    font-size: 1.4rem;
}

.pricing-header {
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.pricing-plan {
    font-weight: 600;
    color: var(--text-primary);
}

.pricing-amount {
    color: var(--vertoie-orange);
    font-weight: 700;
    font-size: 1.6rem;
}

.pricing-details {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    color: var(--text-secondary);
    font-size: 1.2rem;
}

.pricing-item {
    display: flex;
    align-items: center;
    gap: var(--space-1);
}

.pricing-icon {
    width: 1.4rem;
    height: 1.4rem;
    color: var(--color-success);
    flex-shrink: 0;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: var(--bg-primary);
    border-radius: 12px;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: slideUp 0.3s ease;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-6);
    border-bottom: 1px solid var(--border-light);
}

.modal-header h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

.modal-close {
    background: transparent;
    border: none;
    font-size: 2.4rem;
    color: var(--text-muted);
    cursor: pointer;
    transition: all 0.2s ease;
    width: 3.2rem;
    height: 3.2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.modal-close:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.modal-body {
    padding: var(--space-6);
}

.modal-body p {
    font-size: 1.6rem;
    line-height: 1.6;
    color: var(--text-secondary);
    margin: 0 0 var(--space-4) 0;
}

.modal-benefit {
    padding: var(--space-4);
    background: var(--bg-tertiary);
    border-radius: 8px;
    font-size: 1.4rem;
    color: var(--text-primary);
}