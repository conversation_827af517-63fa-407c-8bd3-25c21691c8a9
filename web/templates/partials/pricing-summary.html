{{if .Plan}}
<div class="pricing-summary card" id="pricing-summary">
    <div class="pricing-header">
        <div class="pricing-plan-info">
            <h4 class="pricing-plan-name">{{.Plan.Name}} Plan</h4>
            <div class="pricing-amount">
                <span class="amount">{{.Plan.GetPriceDisplay}}</span>
                {{if ne .Plan.BillingCycle "custom"}}
                <span class="period">/month</span>
                {{end}}
            </div>
        </div>
        {{if ne .Plan.Slug "enterprise"}}
        <div class="pricing-badge">
            <svg class="pricing-badge-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            Recommended
        </div>
        {{end}}
    </div>

    <div class="pricing-details">
        <div class="pricing-item pricing-item-primary">
            <svg class="pricing-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
            </svg>
            <div class="pricing-item-content">
                <span class="pricing-item-label">Modules Selected</span>
                <span class="pricing-item-value">{{.ModuleCount}} of {{.Plan.GetMaxModules}}{{if eq .Plan.GetMaxModules -1}}∞{{end}}</span>
            </div>
        </div>

        <div class="pricing-item">
            <svg class="pricing-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
            </svg>
            <div class="pricing-item-content">
                <span class="pricing-item-label">Team Size</span>
                <span class="pricing-item-value">{{.Plan.GetUsersDisplay}}</span>
            </div>
        </div>

        {{if .Plan.SupportsAPIAccess}}
        <div class="pricing-item">
            <svg class="pricing-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
            </svg>
            <div class="pricing-item-content">
                <span class="pricing-item-label">API Access</span>
                <span class="pricing-item-value">Included</span>
            </div>
        </div>
        {{end}}

        {{if .Plan.HasPrioritySupport}}
        <div class="pricing-item">
            <svg class="pricing-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 2.25a9.75 9.75 0 109.75 9.75A9.75 9.75 0 0012 2.25z"></path>
            </svg>
            <div class="pricing-item-content">
                <span class="pricing-item-label">Support</span>
                <span class="pricing-item-value">Priority</span>
            </div>
        </div>
        {{end}}
    </div>
</div>
{{else}}
<div class="pricing-summary card" id="pricing-summary">
    <div class="pricing-loading">
        <svg class="spinner" viewBox="0 0 24 24">
            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="3" fill="none"/>
        </svg>
        <span>Calculating pricing...</span>
    </div>
</div>
{{end}}