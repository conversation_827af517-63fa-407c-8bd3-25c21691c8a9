{{if .Plan}}
<div class="pricing-summary-compact" id="pricing-summary">
    <div class="pricing-main">
        <div class="pricing-plan-info">
            <span class="pricing-plan-name">{{.Plan.Name}}</span>
            <div class="pricing-amount">
                <span class="amount">{{.Plan.GetPriceDisplay}}</span>
                {{if ne .Plan.BillingCycle "custom"}}
                <span class="period">/mo</span>
                {{end}}
            </div>
        </div>
        <div class="pricing-modules">
            <span class="modules-count">{{.ModuleCount}}</span>
            <span class="modules-label">modules</span>
        </div>
    </div>

    <div class="pricing-features">
        <span class="feature-item">{{.Plan.GetUsersDisplay}}</span>
        {{if .Plan.SupportsAPIAccess}}
        <span class="feature-item">API Access</span>
        {{end}}
        {{if .Plan.HasPrioritySupport}}
        <span class="feature-item">Priority Support</span>
        {{end}}
    </div>
</div>
{{else}}
<div class="pricing-summary-compact" id="pricing-summary">
    <div class="pricing-loading">
        <svg class="spinner" viewBox="0 0 24 24">
            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="3" fill="none"/>
        </svg>
        <span>Loading...</span>
    </div>
</div>
{{end}}