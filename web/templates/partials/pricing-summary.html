{{if .Plan}}
<div class="pricing-summary" id="pricing-summary">
    <div class="pricing-header">
        <div class="pricing-plan">{{.Plan.Name}} Plan</div>
        <div class="pricing-amount">{{.Plan.GetPriceDisplay}}/month</div>
    </div>
    <div class="pricing-details">
        <div class="pricing-item">
            <svg class="pricing-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span>{{.ModuleCount}} modules selected</span>
        </div>
        <div class="pricing-item">
            <svg class="pricing-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span>{{.Plan.GetModulesDisplay}}</span>
        </div>
        <div class="pricing-item">
            <svg class="pricing-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span>{{.Plan.GetUsersDisplay}}</span>
        </div>
    </div>
</div>
{{end}}