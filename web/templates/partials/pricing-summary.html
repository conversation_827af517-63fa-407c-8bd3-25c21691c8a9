{{if .Plan}}
<div class="pricing-bar-inline" id="pricing-summary">
    <span class="plan-name">{{.Plan.Name}} Plan</span>
    <span class="plan-price">{{.Plan.GetPriceDisplay}}{{if ne .Plan.BillingCycle "custom"}}/mo{{end}}</span>
    <span class="modules-selected">{{.ModuleCount}} modules selected</span>
</div>
{{else}}
<div class="pricing-bar-inline" id="pricing-summary">
    <span class="pricing-loading">
        <svg class="spinner" viewBox="0 0 24 24">
            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="3" fill="none"/>
        </svg>
        Loading...
    </span>
</div>
{{end}}