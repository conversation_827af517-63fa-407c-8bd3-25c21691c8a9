<!-- Module Selection View -->
<div class="onboarding-container modules-view">
<div class="onboarding-modules-wrapper">
<div class="onboarding-split">
    <!-- Chat Panel (33%) -->
    <div class="chat-panel">
        <div class="chat-header">
            <h3>Refine Your Software</h3>
            <p>Tell us more about your needs</p>
        </div>
        
        <div class="chat-messages" id="chat-messages">
            <div class="message user">
                <span>{{.UserMessage}}</span>
            </div>
            <div class="message assistant">
                <span>I've analyzed your {{.BusinessType}} and selected relevant software modules. You can customize these selections or tell me more about your specific needs.</span>
            </div>
        </div>
        
        <form class="chat-form" 
              hx-post="/api/business-context/chat-htmx" 
              hx-target="#chat-messages" 
              hx-swap="beforeend"
              hx-trigger="submit"
              hx-on::after-request="this.reset(); htmx.ajax('GET', '/api/onboarding-modules/{{.Organization.ID}}', {target: '#modules-grid'})">
            <input type="hidden" name="organization_id" value="{{.Organization.ID}}">
            <textarea name="message" 
                      class="chat-input" 
                      placeholder="e.g., We need inventory tracking for our 500+ products..."
                      rows="3"
                      required></textarea>
            <button type="submit" class="btn-send">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M22 2L11 13M22 2L15 22L11 13L2 9L22 2Z"/>
                </svg>
            </button>
        </form>
    </div>

    <!-- Modules Panel (67%) -->
    <div class="modules-panel">
        <!-- Sticky Pricing Bar -->
        <div class="pricing-bar" id="pricing-bar">
            <div class="pricing-content">
                <div class="modules-header">
                    <h2>Your Custom Software Modules</h2>
                    <p class="subtitle">Select the features you need</p>
                </div>
                <div id="pricing" class="pricing-box" hx-get="/api/pricing/calculate/{{.Organization.ID}}" hx-trigger="load">
                    <!-- Pricing will load here -->
                </div>
            </div>
        </div>

        <div id="modules-grid">
            <div class="modules-grid">
            {{range .Modules}}
            <div class="module-card {{if or (eq .Status "accepted") (eq .Status "recommended")}}selected{{end}}" data-module-id="{{.ID}}">
                <div class="module-header">
                    <h4>{{.Name}}</h4>
                    <input type="checkbox" 
                           class="module-checkbox"
                           {{if or (eq .Status "accepted") (eq .Status "recommended")}}checked{{end}}
                           hx-post="/api/modules/toggle"
                           hx-vals='{"module_id": "{{.ID}}", "organization_id": "{{$.Organization.ID}}"}'
                           hx-target="#pricing"
                           hx-swap="innerHTML"
                           hx-trigger="change">
                </div>
                <div class="module-content" onclick="showModuleDetails('{{.ID}}', '{{.Name}}', '{{.Description}}', '{{.KeyBenefit}}')">
                    <p class="module-desc">{{.Description}}</p>
                    {{if .KeyBenefit}}
                    <p class="module-benefit">✓ {{.KeyBenefit}}</p>
                    {{end}}
                </div>
                <button class="module-expand" onclick="showModuleDetails('{{.ID}}', '{{.Name}}', '{{.Description}}', '{{.KeyBenefit}}')">View Details</button>
            </div>
            {{end}}
            </div>
        </div>

        <div class="complete-section">
            <button class="btn btn-primary btn-large" 
                    hx-post="/api/onboarding/complete"
                    hx-on::after-request="if(event.detail.successful) window.location.href = '/dashboard'">
                Create My Software
            </button>
        </div>
    </div>
</div>
</div>

<!-- Module Details Modal -->
<div id="module-modal" class="modal" onclick="if(event.target === this) closeModal()">
    <div class="modal-content">
        <div class="modal-header">
            <h3 id="modal-title"></h3>
            <button class="modal-close" onclick="closeModal()">&times;</button>
        </div>
        <div class="modal-body">
            <p id="modal-description"></p>
            <div id="modal-benefit" class="modal-benefit"></div>
        </div>
    </div>
</div>

<script>
// Module selection handling
document.addEventListener('change', function(e) {
    if (e.target.classList.contains('module-checkbox')) {
        const card = e.target.closest('.module-card');
        if (e.target.checked) {
            card.classList.add('selected');
        } else {
            card.classList.remove('selected');
        }
    }
});

// Modal functions
function showModuleDetails(id, name, description, benefit) {
    document.getElementById('modal-title').textContent = name;
    document.getElementById('modal-description').textContent = description;
    if (benefit && benefit !== '') {
        document.getElementById('modal-benefit').innerHTML = '<strong>Key Benefit:</strong> ' + benefit;
    } else {
        document.getElementById('modal-benefit').innerHTML = '';
    }
    document.getElementById('module-modal').style.display = 'flex';
}

function closeModal() {
    document.getElementById('module-modal').style.display = 'none';
}

// Close modal on Escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeModal();
    }
});
</script>
</div>

